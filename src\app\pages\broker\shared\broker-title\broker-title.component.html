<div class="card mb-1 mb-xl-1" *ngIf="user?.role === 'broker'">
  <div class="card-body pt-5 pb-0">
    <div class="row align-items-center mb-3">
      <div class="col-12 col-sm-auto mb-4 mb-sm-0 text-center text-sm-start">
        <div class="symbol position-relative mx-auto mx-sm-0"
          [ngClass]="currentLang === 'ar' ? 'profile-image-arabic' : 'symbol-65px symbol-fixed'">
          <img [src]="user.image" [alt]="'BROKER_TITLE.USER_ALT' | translate" />
        </div>
      </div>

      <div class="col">
        <div class="row justify-content-between">
          <div class="col-12 col-lg-9">
            <div class="d-flex align-items-center flex-wrap mb-2" *ngIf="user?.fullName">

              <span class="text-gray-800 fs-2 fw-bolder me-1" *ngIf="currentLang === 'en'">
                {{ 'BROKER_TITLE.HELLO' | translate }}
              </span>
              <a [routerLink]="['/broker/profile']" class="text-gray-800 text-hover-dark-blue fs-2 fw-semibold me-1"
                [style.font-family]="currentLang === 'ar' ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                {{ capitalizeWords(user?.fullName) }}
              </a>
              <!-- <span class="badge fw-bolder ms-2 fs-6 py-1 px-3 user-badge" *ngIf="user.accountType"
                [ngClass]="getAccountTypeBadge(user.accountType)"
                [style.font-family]="currentLang === 'ar' ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ getTranslatedAccountType(user?.accountType) }}
              </span> -->
            </div>

            <div class="row">
              <div class="col-12 d-flex flex-wrap">
                <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge"
                  [ngClass]="getAccountTypeBadge(user.accountType)">
                  {{ getTranslatedAccountType(user?.accountType) }}
                </span>

                <ng-container *ngFor="let specialization of user?.specializationScopes">
                  <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge badge-mid-blue"
                    [style.font-family]="currentLang === 'ar' ? 'Markazi Text' : 'Noto Kufi Arabic'">
                    {{ getTranslatedSpecialization(specialization?.specialization) }}
                  </span>
                </ng-container>

                <ng-container *ngFor="let area of user?.areas">
                  <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                    {{ currentLang === 'ar' ? (area?.name_ar || area?.name_en) : area?.name_en }}
                  </span>
                </ng-container>
              </div>
            </div>
          </div>

          <div class="col-12 col-lg-auto mt-3 mt-lg-0"
            [ngClass]="currentLang === 'ar' ? 'button-container-arabic' : 'text-lg-end'"
            *ngIf="showCreateButton && hasPermission('create_request')">
            <a [routerLink]="['/broker/stepper-modal']" class="btn btn-sm btn-dark-blue btn-active-light-dark-blue"
              [style.font-family]="currentLang === 'ar' ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.margin-top]="currentLang === 'ar' ? '0.5rem' : '2.25rem'">
              <i class="fa-solid fa-plus" [class.me-1]="currentLang !== 'ar'" [class.ms-1]="currentLang === 'ar'"></i>
              {{ 'BROKER_TITLE.CREATE_NEW_REQUEST' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="card mb-1 mb-xl-1" *ngIf="user?.role === 'client'">
  <div class="card-body pt-5 pb-0">
    <div class="row align-items-center mb-3">
      <div class="col-12 col-sm-auto mb-4 mb-sm-0 text-center text-sm-start">
        <div class="symbol position-relative mx-auto mx-sm-0"
          [ngClass]="currentLang === 'ar' ? 'profile-image-arabic' : 'symbol-65px symbol-fixed'">
          <img [src]="user.image" [alt]="'BROKER_TITLE.PROFILE_IMAGE_ALT' | translate" *ngIf="user.image" />
        </div>
      </div>

      <div class="col">
        <div class="row justify-content-between">
          <div class="col-12 col-lg-9">
            <div class="d-flex align-items-center flex-wrap mb-2" *ngIf="user?.fullName">
              <!-- Show "Hello -" only in English -->
              <span class="text-gray-800 fs-2 fw-bolder me-1" *ngIf="currentLang === 'en'">
                {{ 'BROKER_TITLE.HELLO' | translate }}
              </span>
              <a [routerLink]="['/broker/profile']" class="text-gray-800 text-hover-dark-blue fs-2 fw-semibold me-1"
                [style.font-family]="currentLang === 'ar' ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                {{ capitalizeWords(user?.fullName) }}
              </a>
            </div>
          </div>

          <div class="col-12 col-lg-auto"
            *ngIf="showCreateButton && hasPermission('create_request') && currentLang === 'en'">
            <a [routerLink]="['/broker/stepper-modal']"
              class="btn btn-sm btn-dark-blue btn-active-light-dark-blue mt-0">
              <i class="fa-solid fa-plus me-1"></i>
              {{ 'BROKER_TITLE.CREATE_NEW_REQUEST' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>